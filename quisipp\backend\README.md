# Quisipp Backend API

A Dart backend server for the Quisipp food delivery app using Shelf framework and MongoDB.

## Features

- **Product Management**: Load products by category, get all products, and fetch individual products
- **Category Management**: Get all available product categories
- **MongoDB Integration**: Uses MongoDB for data storage
- **CORS Support**: Enabled for cross-origin requests
- **Error Handling**: Comprehensive error handling with proper HTTP status codes

## API Endpoints

### Health Check
- **GET** `/health` - Check if the server is running

### Products
- **GET** `/api/products` - Get all active products
- **GET** `/api/products/category/<category>` - Get products by category (case-insensitive)
- **GET** `/api/products/categories` - Get all unique categories
- **GET** `/api/products/id/<id>` - Get a specific product by ID

## Setup Instructions

### Prerequisites
- Dart SDK (^3.8.1)
- MongoDB database
- `.env` file with MongoDB connection string

### Installation

1. **Install dependencies:**
   ```bash
   cd backend
   dart pub get
   ```

2. **Create `.env` file:**
   ```
   MONGO_URI=mongodb://localhost:27017/your_database_name
   # or for MongoDB Atlas:
   # MONGO_URI=mongodb+srv://username:<EMAIL>/database_name
   ```

3. **Run the server:**
   ```bash
   dart run server.dart
   ```

The server will start on `http://localhost:8080`

## Database Schema

Your MongoDB collection `adminproducts` should have documents with the following structure:

```json
{
  "_id": "6871fba0f848cecb74012693",
  "productName": "Fortune Premium Kachi Ghani Pure Mustard Oil (910 g)",
  "productDescription": "Product Details Description Fortune Premium Kachi Ghani Pure Mustard…",
  "productPrice": 165,
  "productCategory": "oil & ghee",
  "productBrand": "Fortune",
  "availableWeights": [],
  "cloudinaryUrls": [],
  "createdBy": "686700646fa020ad88517215",
  "isActive": true,
  "adoptionCount": 0,
  "tags": [],
  "searchKeywords": [],
  "createdAt": "2025-07-12T06:07:28.321+00:00",
  "updatedAt": "2025-07-12T06:07:28.321+00:00",
  "__v": 0
}
```

## API Response Format

All API responses follow this format:

### Success Response
```json
{
  "success": true,
  "data": [...],
  "count": 10,
  "category": "oil & ghee" // only for category-specific requests
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message description"
}
```

## Testing

Run the test script to verify all endpoints:

```bash
dart run test_api.dart
```

Make sure your server is running before executing the test script.

## Example Usage

### Get products by category
```bash
curl "http://localhost:8080/api/products/category/oil%20%26%20ghee"
```

### Get all categories
```bash
curl "http://localhost:8080/api/products/categories"
```

### Get all products
```bash
curl "http://localhost:8080/api/products"
```

## Error Handling

The API includes comprehensive error handling:
- **400 Bad Request**: Missing or invalid parameters
- **404 Not Found**: Product or resource not found
- **500 Internal Server Error**: Database or server errors

All errors return a JSON response with `success: false` and an error message.

## CORS Configuration

CORS is enabled for all origins to support frontend development. In production, configure CORS to allow only specific domains.
