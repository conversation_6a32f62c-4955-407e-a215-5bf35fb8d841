import 'package:mongo_dart/mongo_dart.dart';
import 'package:dotenv/dotenv.dart';

class DatabaseConnection {
  static Db? db;

  static Future<void> connect() async {
    final dotEnv = DotEnv();

    // Try multiple possible locations for the .env file
    final possiblePaths = ['.env', 'backend/.env', '../.env'];
    bool loaded = false;

    for (final path in possiblePaths) {
      try {
        dotEnv.load([path]);
        print('✅ Loaded .env file from: $path');
        loaded = true;
        break;
      } catch (e) {
        print('⚠️  Could not load .env from $path: $e');
      }
    }

    if (!loaded) {
      throw Exception(
        'Could not find .env file in any of the expected locations: ${possiblePaths.join(', ')}',
      );
    }

    final mongoUri = dotEnv['MONGO_URI'];
    if (mongoUri == null || mongoUri.isEmpty) {
      throw Exception('MONGO_URI not found or empty in .env file');
    }

    print('🔗 Connecting to MongoDB...');

    // Use Db.create() for mongodb+srv URIs, Db() for regular mongodb URIs
    if (mongoUri.startsWith('mongodb+srv://')) {
      db = await Db.create(mongoUri);
    } else {
      db = Db(mongoUri);
    }
    await db!.open();
    print('✅ Connected to MongoDB successfully!');
  }

  static Future<void> close() async {
    await db!.close();
    print('Disconnected from MongoDB');
  }

  // static Future<void> createCollection(String name) async {
  //   await db!.createCollection(name);
  //   print('Collection $name created');
  // }

  // static Future<void> insertDocument(
  //     String collectionName, Map<String, dynamic> document) async {
  //   await db!.collection(collectionName).insertOne(document);
  //   print('Document inserted');
  // }

  // static Future<void> findDocuments(String collectionName) async {
  //   var result = await db!.collection(collectionName).find().toList();
  //   print('Found ${result.length} documents');
  // }
}
