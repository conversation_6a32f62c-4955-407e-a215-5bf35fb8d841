import 'dart:convert';
import 'package:http/http.dart' as http;

class ProductService {
  static const String baseUrl = 'http://10.161.178.104:8080/api';

  // Get all products
  static Future<List<Map<String, dynamic>>> getAllProducts() async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/products'));

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true) {
          return List<Map<String, dynamic>>.from(jsonResponse['data']);
        }
      }
      throw Exception('Failed to load products');
    } catch (e) {
      throw Exception('Error fetching products: $e');
    }
  }

  // Get products by category
  static Future<List<Map<String, dynamic>>> getProductsByCategory(
    String category,
  ) async {
    try {
      final encodedCategory = Uri.encodeComponent(category);
      final response = await http.get(
        Uri.parse('$baseUrl/products/category/$encodedCategory'),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true) {
          return List<Map<String, dynamic>>.from(jsonResponse['data']);
        }
      }
      throw Exception('Failed to load products for category: $category');
    } catch (e) {
      throw Exception('Error fetching products by category: $e');
    }
  }

  // Get all categories
  static Future<List<String>> getAllCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/products/categories'),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true) {
          return List<String>.from(jsonResponse['data']);
        }
      }
      throw Exception('Failed to load categories');
    } catch (e) {
      throw Exception('Error fetching categories: $e');
    }
  }

  // Get product by ID
  static Future<Map<String, dynamic>?> getProductById(String id) async {
    try {
      final response = await http.get(Uri.parse('$baseUrl/products/id/$id'));

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['success'] == true) {
          return Map<String, dynamic>.from(jsonResponse['data']);
        }
      } else if (response.statusCode == 404) {
        return null; // Product not found
      }
      throw Exception('Failed to load product');
    } catch (e) {
      throw Exception('Error fetching product: $e');
    }
  }
}
