import 'dart:io';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart';
import 'package:shelf_router/shelf_router.dart';
import 'package:shelf_cors_headers/shelf_cors_headers.dart';

import 'db/db.dart';
import 'routes/product_route.dart';

void main() async {
  await DatabaseConnection.connect();

  final router = Router();

  // Add routes
  router.mount('/api/', ProductRoute().router.call);

  // Health check endpoint
  router.get('/health', (Request request) {
    return Response.ok('Server is running and healthy!');
  });

  // Default handler for root
  router.get('/', (Request request) {
    return Response.ok('Welcome to Quisipp API Server!');
  });

  final handler = const Pipeline()
      .addMiddleware(corsHeaders())
      .addMiddleware(logRequests())
      .addHandler(router.call);

  await serve(handler, InternetAddress.anyIPv4, 8080);

  print('✅ Server running on http://localhost:8080');
  print('📋 Available endpoints:');
  print('   GET  /health - Health check');
  print('   GET  /api/products - Get all products');
  print('   GET  /api/products/category/<category> - Get products by category');
  print('   GET  /api/products/categories - Get all categories');
  print('   GET  /api/products/id/<id> - Get product by ID');
}
