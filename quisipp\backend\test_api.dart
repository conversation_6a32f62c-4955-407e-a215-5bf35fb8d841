import 'dart:convert';
import 'dart:io';

void main() async {
  final client = HttpClient();
  final baseUrl = 'http://localhost:8080';

  print('🧪 Testing Quisipp API Endpoints...\n');

  try {
    // Test health endpoint
    await testEndpoint(client, '$baseUrl/health', 'Health Check');

    // Test get all categories
    await testEndpoint(client, '$baseUrl/api/products/categories', 'Get All Categories');

    // Test get products by category (using "oil & ghee" from your sample data)
    await testEndpoint(client, '$baseUrl/api/products/category/oil%20%26%20ghee', 'Get Products by Category (oil & ghee)');

    // Test get all products
    await testEndpoint(client, '$baseUrl/api/products', 'Get All Products');

    // Test get product by ID (you'll need to replace with actual ID from your database)
    // await testEndpoint(client, '$baseUrl/api/products/id/6871fba0f848cecb74012693', 'Get Product by ID');

  } catch (e) {
    print('❌ Error during testing: $e');
  } finally {
    client.close();
  }
}

Future<void> testEndpoint(HttpClient client, String url, String testName) async {
  try {
    print('🔍 Testing: $testName');
    print('📡 URL: $url');
    
    final request = await client.getUrl(Uri.parse(url));
    final response = await request.close();
    
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('📊 Status: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(responseBody);
      if (jsonResponse is Map && jsonResponse['success'] == true) {
        print('✅ Success!');
        if (jsonResponse['count'] != null) {
          print('📈 Count: ${jsonResponse['count']}');
        }
        if (jsonResponse['data'] is List) {
          final data = jsonResponse['data'] as List;
          if (data.isNotEmpty) {
            print('📝 Sample item: ${data.first.runtimeType}');
          }
        }
      } else {
        print('⚠️  Response format unexpected');
      }
    } else {
      print('❌ Failed with status: ${response.statusCode}');
      print('📄 Response: $responseBody');
    }
    
    print('─' * 50);
    
  } catch (e) {
    print('❌ Error testing $testName: $e');
    print('─' * 50);
  }
}
