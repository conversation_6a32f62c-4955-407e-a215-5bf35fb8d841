import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// import 'package:quisipp/screens/home_screen.dart';

// define a class for the onboarding screen
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

// define _OnboardingScreenState class
class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();

  int _currentIndex = 0;

  final List<Map<String, String>> _pages = [
    {
      "title": "Welcome to Quisipp",
      "description":
          "Experience lightning-fast shopping—order anything and get it delivered to you in minutes with <PERSON><PERSON><PERSON><PERSON>!",
      "image": "assets/images/onboarding/FullTrolley.png",
    },
    {
      "title": "Get any packages delivered",
      "description":
          "We deliver to your doorstep, so you can enjoy your favorite food without leaving your home.",
      "image": "assets/images/onboarding/DeliveryTruck.png",
    },
    {
      "title": "Protected package delivery.",
      "description":
          "Your groceries are carefully packaged to ensure they arrive safely and in perfect condition.",
      "image": "assets/images/onboarding/BigBox.png",
    },
    {
      "title": "Best price guaranteed",
      "description":
          "Allowing you to stock up on your favorite items while staying within your budget.",
      "image": "assets/images/onboarding/CashRegister.png",
    },
  ];

  // function to navigate to the next page
  void _nextPage() {
    if (_currentIndex < _pages.length - 1) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.pushReplacementNamed(context, '/login');
    }
  }

  // function to skip the onboarding screen
  void _skip() {
    Navigator.pushReplacementNamed(context, '/login');
  }
  

  // widget to build the stepper indicator
  Widget _buildStepperIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => AnimatedContainer(
          duration: const Duration(microseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: 70,
          height: 5,
          decoration: BoxDecoration(
            color: _currentIndex == index
                ? Colors.black
                : Colors.grey.withValues(alpha: 0.4),
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  // widget to build the onboarding page
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: const Color.fromARGB(240, 214, 149, 137),
      body: SafeArea(
        
        child: Column(
          children: [
            // stepper indicator
            Padding(
              padding: const EdgeInsets.only(top: 40),
              child: _buildStepperIndicator(),
            ),

            // page view
            Expanded(
              child: PageView.builder(
                controller: _controller,
                itemCount: _pages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemBuilder: (contex, index) {
                  final item = _pages[index];
                  return Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AnimatedBuilder(
                          animation: _controller,
                          builder: (context, child) {
                            double page = _currentIndex.toDouble();
                            try {
                              if (_controller.hasClients && _controller.page != null) {
                                page = _controller.page!;
                              }
                            } catch (_) {}
                            double delta = (index - page);

                            // For the second last page, use a smooth bounce-in effect
                            if (index == _pages.length - 2) {
                              double t = (1.0 - delta.abs()).clamp(0.0, 1.0);
                              // Bounce in from top, but less "springy" to avoid glitch
                              double bounce = -200 * (1 - t) + 20 * (1 - t) * (1 - t);
                              return AnimatedOpacity(
                                opacity: delta.abs() < 1.0 ? 1.0 : 0.0,
                                duration: const Duration(milliseconds: 400),
                                child: Transform.translate(
                                  offset: Offset(0, bounce),
                                  child: Image.asset(
                                    item["image"]!,
                                    height: 200,
                                    width: 200,
                                  ),
                                ),
                              );
                            } else {
                              // All other pages: simple fall from top
                              double t = (1.0 - delta.abs()).clamp(0.0, 1.0);
                              double y = -200 * (1 - t);
                              return AnimatedOpacity(
                                opacity: delta.abs() < 1.0 ? 1.0 : 0.0,
                                duration: const Duration(milliseconds: 400),
                                child: Transform.translate(
                                  offset: Offset(0, y),
                                  child: Image.asset(
                                    item["image"]!,
                                    height: 200,
                                    width: 200,
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                        const SizedBox(height: 30),
                        Text(
                          item["title"]!,
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          item["description"]!,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: Colors.black.withValues(alpha: 0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            // stepper indicator
            // _buildStepperIndicator(),

            // bottom navigation
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 24.0,
                vertical: 20,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: _currentIndex == _pages.length - 1
                    ? [
                        Expanded(
                          child: SizedBox(
                            height: 48,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 60),
                                backgroundColor: const Color.fromARGB(255, 17, 17, 17),
                              ),
                              onPressed: _nextPage,
                              child: Text(
                                "Get Started",
                                style: GoogleFonts.lato(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ]
                    : [
                        SizedBox(
                          height: 48,
                          child: TextButton(
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 60),
                              backgroundColor: Colors.grey.shade200,
                            ),
                            onPressed: () {
                              if (_controller.hasClients) {
                                _controller.animateToPage(
                                  _pages.length - 1,
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                              _skip();
                            },
                            child: Text(
                              "Skip",
                              style: GoogleFonts.lato(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 48,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(horizontal: 60),
                              backgroundColor: const Color.fromARGB(255, 17, 17, 17),
                              // keep color always the same
                              foregroundColor: Colors.white,
                              disabledBackgroundColor: const Color.fromARGB(255, 17, 17, 17),
                              disabledForegroundColor: Colors.white,
                            ),
                            onPressed: () {
                              if (_controller.hasClients) {
                                _controller.animateToPage(
                                  (_currentIndex + 1).clamp(0, _pages.length - 1),
                                  duration: const Duration(milliseconds: 500),
                                  curve: Curves.easeInOut,
                                );
                              }
                            },
                            child: Text(
                              "Next",
                              style: GoogleFonts.lato(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
