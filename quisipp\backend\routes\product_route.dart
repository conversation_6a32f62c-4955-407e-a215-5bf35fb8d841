import 'package:shelf/shelf.dart';
import 'package:shelf_router/shelf_router.dart';
import 'dart:convert';
import 'package:mongo_dart/mongo_dart.dart' show ObjectId, BsonBinary;
import 'package:mongo_dart/mongo_dart.dart' show where;

// database
import '../db/db.dart';

class ProductRoute {
  // Helper function to convert MongoDB documents to JSON-safe format
  static Map<String, dynamic> _sanitizeDocument(Map<String, dynamic> doc) {
    final sanitized = <String, dynamic>{};

    for (final entry in doc.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is ObjectId) {
        sanitized[key] = value.oid;
      } else if (value is DateTime) {
        sanitized[key] = value.toIso8601String();
      } else if (value is BsonBinary) {
        // Skip binary data or convert to base64 if needed
        continue;
      } else if (value is List) {
        sanitized[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _sanitizeDocument(item);
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = _sanitizeDocument(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  Router get router {
    final router = Router();

    // Get all products
    router.get('/products', (Request request) async {
      try {
        final products = await DatabaseConnection.db!
            .collection('adminproducts')
            .find({'isActive': true})
            .toList();

        // Sanitize the products to make them JSON-safe
        final sanitizedProducts = products
            .map((product) => _sanitizeDocument(product))
            .toList();

        return Response.ok(
          jsonEncode({
            'success': true,
            'data': sanitizedProducts,
            'count': sanitizedProducts.length,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } catch (e) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Failed to fetch products: ${e.toString()}',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    });

    // Get products by category
    router.get('/products/category/<category>', (Request request) async {
      try {
        final category = request.params['category'];

        if (category == null || category.isEmpty) {
          return Response.badRequest(
            body: jsonEncode({
              'success': false,
              'error': 'Category parameter is required',
            }),
            headers: {'Content-Type': 'application/json'},
          );
        }

        // URL decode the category parameter
        final decodedCategory = Uri.decodeComponent(category);

        // Case-insensitive search for category
        final products =
            await DatabaseConnection.db!.collection('adminproducts').find({
              'productCategory': RegExp(decodedCategory, caseSensitive: false),
              'isActive': true,
            }).toList();

        // Sanitize the products to make them JSON-safe
        final sanitizedProducts = products
            .map((product) => _sanitizeDocument(product))
            .toList();

        return Response.ok(
          jsonEncode({
            'success': true,
            'data': sanitizedProducts,
            'category': decodedCategory,
            'count': sanitizedProducts.length,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } catch (e) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Failed to fetch products by category: ${e.toString()}',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    });

    // Get all unique categories
    router.get('/products/categories', (Request request) async {
      try {
        // Get all products and extract unique categories manually
        final products = await DatabaseConnection.db!
            .collection('adminproducts')
            .find(where.eq('isActive', true))
            .toList();

        final categoriesSet = <String>{};
        for (final product in products) {
          final category = product['productCategory'];
          if (category != null && category is String && category.isNotEmpty) {
            categoriesSet.add(category);
          }
        }

        final categories = categoriesSet.toList()..sort();

        return Response.ok(
          jsonEncode({
            'success': true,
            'data': categories,
            'count': categories.length,
          }),
          headers: {'Content-Type': 'application/json'},
        );
      } catch (e) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Failed to fetch categories: ${e.toString()}',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    });

    // Get product by ID
    router.get('/products/id/<id>', (Request request) async {
      try {
        final id = request.params['id'];

        if (id == null || id.isEmpty) {
          return Response.badRequest(
            body: jsonEncode({
              'success': false,
              'error': 'Product ID is required',
            }),
            headers: {'Content-Type': 'application/json'},
          );
        }

        // Try to parse the ID as ObjectId if it looks like one
        dynamic queryId = id;
        if (ObjectId.isValidHexId(id)) {
          queryId = ObjectId.fromHexString(id);
        }

        final product = await DatabaseConnection.db!
            .collection('adminproducts')
            .findOne({'_id': queryId, 'isActive': true});

        if (product == null) {
          return Response.notFound(
            jsonEncode({'success': false, 'error': 'Product not found'}),
            headers: {'Content-Type': 'application/json'},
          );
        }

        // Sanitize the product to make it JSON-safe
        final sanitizedProduct = _sanitizeDocument(product);

        return Response.ok(
          jsonEncode({'success': true, 'data': sanitizedProduct}),
          headers: {'Content-Type': 'application/json'},
        );
      } catch (e) {
        return Response.internalServerError(
          body: jsonEncode({
            'success': false,
            'error': 'Failed to fetch product: ${e.toString()}',
          }),
          headers: {'Content-Type': 'application/json'},
        );
      }
    });

    return router;
  }
}
