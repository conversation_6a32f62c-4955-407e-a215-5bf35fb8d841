import 'package:flutter/material.dart';
import 'package:quisipp/screens/all_products_screen.dart';
import 'package:quisipp/screens/categories_screen.dart';
import 'package:quisipp/screens/cart_screen.dart';
import '../services/cart_service.dart';
// import '../widgets/animated_button.dart';
import 'explore_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quisipp Food App'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          AnimatedBuilder(
            animation: CartService(),
            builder: (context, child) {
              final cartService = CartService();
              final itemCount = cartService.itemCount;

              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.shopping_cart),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const CartScreen()),
                      );
                    },
                  ),
                  if (itemCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          itemCount.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(color: Colors.green),
              child: const Text(
                'Menu',
                style: TextStyle(color: Colors.white, fontSize: 24),
              ),
            ),
            ListTile(
              title: const Text('Explore Screen'),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const ExploreScreen()),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.shopping_cart),
              title: const Text('Shopping Cart'),
              onTap: () {
                Navigator.pop(context); // Close drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const CartScreen()),
                );
              },
            ),
          ],
        ),
      ),

      body: const Column(
        children: [
          // Show CategoriesScreen at the top
          Expanded(flex: 2, child: CategoriesScreen()),
          // Show AllProductsScreen at the bottom
          Expanded(flex: 3, child: AllProductsScreen()),
        ],
      ),

      // body: Center(
      //   child: AnimatedButton(
      //     label: 'Click Me',
      //     onTap: () {
      //       Navigator.push(context, MaterialPageRoute(builder: (_) => ExploreScreen()));
      //     },
      //   ),
      // ),
    );
  }
}
